<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\client\mp\alipay;

use app\store\controller\Controller;
use app\store\model\mp\alipay\Setting as SettingModel;
use think\response\Json;

/**
 * 支付宝小程序设置
 * Class Setting
 * @package app\store\controller\client\mp\alipay
 */
class Setting extends Controller
{
    /**
     * 获取支付宝小程序基础设置
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function basic(): Json
    {
        $detail = SettingModel::getBasic();
        // 服务端域名
        $domain = $this->request->host(true);
        return $this->renderSuccess(compact('detail', 'domain'));
    }

    /**
     * 获取支付宝小程序设置 (指定)
     * @param string $key
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(string $key): Json
    {
        // 获取支付宝小程序设置
        $detail = SettingModel::getItem($key);
        // 服务端域名
        $domain = $this->request->host(true);
        return $this->renderSuccess(compact('detail', 'domain'));
    }

    /**
     * 更新设置项
     * @param string $key
     * @return Json
     */
    public function update(string $key): Json
    {
        $model = new SettingModel;
        if ($model->edit($key, $this->postForm())) {
            return $this->renderSuccess([], '更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 更新基础设置
     * @return Json
     */
    public function updateBasic(): Json
    {
        $model = new SettingModel;
        if ($model->edit('basic', $this->postForm())) {
            return $this->renderSuccess([], '更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 测试支付宝小程序配置
     * @return Json
     */
    public function testConfig(): Json
    {
        try {
            // 获取配置信息
            $config = SettingModel::getBasic();

            // 验证必要参数
            if (empty($config['appId'])) {
                return $this->renderError('请先配置支付宝小程序AppID');
            }

            if (empty($config['merchantPrivateKey'])) {
                return $this->renderError('请先配置商户私钥');
            }

            if ($config['signMode'] == 10 && empty($config['alipayPublicKey'])) {
                return $this->renderError('普通公钥模式下，请配置支付宝公钥');
            }

            if ($config['signMode'] == 20) {
                if (empty($config['appCertPublicKey']) ||
                    empty($config['alipayCertPublicKey']) ||
                    empty($config['alipayRootCert'])) {
                    return $this->renderError('公钥证书模式下，请配置完整的证书信息');
                }
            }

            // 测试配置是否正确
            $class = '\app\common\library\alipay\Oauth';
            if (!class_exists($class)) {
                return $this->renderError('支付宝OAuth类不存在');
            }

            return $this->renderSuccess([], '配置验证通过');

        } catch (\Exception $e) {
            return $this->renderError('配置测试失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付宝小程序配置说明
     * @return Json
     */
    public function getConfigGuide(): Json
    {
        $guide = [
            'steps' => [
                [
                    'title' => '1. 创建支付宝小程序',
                    'content' => '登录支付宝开放平台，创建小程序应用，获取AppID',
                    'url' => 'https://open.alipay.com'
                ],
                [
                    'title' => '2. 配置应用信息',
                    'content' => '在支付宝开放平台配置小程序基本信息、接口权限等'
                ],
                [
                    'title' => '3. 生成密钥',
                    'content' => '生成RSA2密钥对，上传公钥到支付宝开放平台'
                ],
                [
                    'title' => '4. 配置回调地址',
                    'content' => '配置授权回调地址和支付回调地址'
                ]
            ],
            'docs' => [
                [
                    'title' => '支付宝小程序开发文档',
                    'url' => 'https://opendocs.alipay.com/mini'
                ],
                [
                    'title' => '支付宝开放平台',
                    'url' => 'https://open.alipay.com'
                ],
                [
                    'title' => 'RSA密钥生成工具',
                    'url' => 'https://opendocs.alipay.com/common/02kipl'
                ]
            ],
            'tips' => [
                '请妥善保管商户私钥，不要泄露给他人',
                '建议使用公钥证书模式，安全性更高',
                '配置完成后请先测试配置是否正确',
                '支付宝小程序需要在支付宝开放平台审核通过后才能正常使用'
            ]
        ];

        return $this->renderSuccess($guide);
    }

    /**
     * 获取所有支付宝小程序设置
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAll(): Json
    {
        $data = SettingModel::getAll();
        // 服务端域名
        $domain = $this->request->host(true);
        return $this->renderSuccess(compact('data', 'domain'));
    }

    /**
     * 重置支付宝小程序设置
     * @param string $key
     * @return Json
     */
    public function reset(string $key): Json
    {
        $model = new SettingModel;
        $defaultData = $model->defaultData();

        if (!isset($defaultData[$key])) {
            return $this->renderError('设置项不存在');
        }

        if ($model->edit($key, $defaultData[$key]['values'])) {
            return $this->renderSuccess([], '重置成功');
        }
        return $this->renderError($model->getError() ?: '重置失败');
    }

    /**
     * 批量更新设置
     * @return Json
     */
    public function batchUpdate(): Json
    {
        $data = $this->postForm();
        $model = new SettingModel;
        $success = true;
        $errors = [];

        foreach ($data as $key => $values) {
            if (!$model->edit($key, $values)) {
                $success = false;
                $errors[$key] = $model->getError();
            }
        }

        if ($success) {
            return $this->renderSuccess([], '批量更新成功');
        } else {
            return $this->renderError('部分设置更新失败', $errors);
        }
    }

    /**
     * 导出支付宝小程序配置
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function export(): Json
    {
        $data = SettingModel::getAll();

        // 移除敏感信息
        foreach ($data as &$item) {
            if (isset($item['values']['merchantPrivateKey'])) {
                $item['values']['merchantPrivateKey'] = '***';
            }
            if (isset($item['values']['alipayPublicKey'])) {
                $item['values']['alipayPublicKey'] = '***';
            }
        }

        $exportData = [
            'export_time' => date('Y-m-d H:i:s'),
            'store_id' => static::$storeId,
            'data' => $data
        ];

        return $this->renderSuccess($exportData, '导出成功');
    }
}
